/**
 * Theme Management Utility
 * Handles theme switching and customization
 */

export class ThemeManager {
  constructor() {
    this.currentTheme = 'elegant';
    this.themes = {
      elegant: {
        name: 'Elegant',
        colors: {
          primary: '#F1EFEC',
          secondary: '#D4C9BE',
          accent: '#123458',
          dark: '#030303'
        }
      },
      classic: {
        name: 'Classic',
        colors: {
          primary: '#F9FAFB',
          secondary: '#6B7280',
          accent: '#1D4ED8',
          dark: '#111827'
        }
      },
      modern: {
        name: 'Modern',
        colors: {
          primary: '#FAFAFA',
          secondary: '#E5E7EB',
          accent: '#059669',
          dark: '#1F2937'
        }
      }
    };
    
    this.prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)');
    this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    this.prefersHighContrast = window.matchMedia('(prefers-contrast: high)');
  }

  init() {
    this.loadSavedTheme();
    this.setupMediaQueryListeners();
    this.applyTheme();
    this.setupThemeControls();
  }

  loadSavedTheme() {
    const saved = localStorage.getItem('sbo-theme');
    if (saved && this.themes[saved]) {
      this.currentTheme = saved;
    }
  }

  setupMediaQueryListeners() {
    // Listen for system theme changes
    this.prefersDarkMode.addEventListener('change', (e) => {
      this.handleSystemThemeChange(e);
    });

    // Listen for reduced motion preference
    this.prefersReducedMotion.addEventListener('change', (e) => {
      this.handleReducedMotionChange(e);
    });

    // Listen for high contrast preference
    this.prefersHighContrast.addEventListener('change', (e) => {
      this.handleHighContrastChange(e);
    });
  }

  applyTheme(themeName = this.currentTheme) {
    const theme = this.themes[themeName];
    if (!theme) return;

    const root = document.documentElement;
    
    // Apply theme colors
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });

    // Update theme class
    document.body.className = document.body.className
      .replace(/theme-\w+/g, '')
      .trim();
    document.body.classList.add(`theme-${themeName}`);

    // Update meta theme color
    this.updateMetaThemeColor(theme.colors.accent);

    // Save theme preference
    localStorage.setItem('sbo-theme', themeName);
    this.currentTheme = themeName;

    // Dispatch theme change event
    this.dispatchThemeChangeEvent(themeName);
  }

  switchTheme(themeName) {
    if (!this.themes[themeName]) {
      console.warn(`Theme "${themeName}" not found`);
      return;
    }

    // Add transition class for smooth theme switching
    document.body.classList.add('theme-transitioning');
    
    setTimeout(() => {
      this.applyTheme(themeName);
      
      setTimeout(() => {
        document.body.classList.remove('theme-transitioning');
      }, 300);
    }, 50);
  }

  getAvailableThemes() {
    return Object.keys(this.themes).map(key => ({
      key,
      name: this.themes[key].name,
      colors: this.themes[key].colors
    }));
  }

  getCurrentTheme() {
    return {
      key: this.currentTheme,
      ...this.themes[this.currentTheme]
    };
  }

  setupThemeControls() {
    // Setup theme switcher if it exists
    const themeSwitcher = document.querySelector('[data-theme-switcher]');
    if (themeSwitcher) {
      this.createThemeSwitcherUI(themeSwitcher);
    }

    // Setup individual theme buttons
    document.querySelectorAll('[data-theme]').forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        const theme = button.dataset.theme;
        this.switchTheme(theme);
      });
    });
  }

  createThemeSwitcherUI(container) {
    const themes = this.getAvailableThemes();
    
    container.innerHTML = `
      <div class="theme-switcher">
        <label class="theme-switcher-label">Theme:</label>
        <div class="theme-options">
          ${themes.map(theme => `
            <button 
              type="button" 
              class="theme-option ${theme.key === this.currentTheme ? 'active' : ''}"
              data-theme="${theme.key}"
              title="Switch to ${theme.name} theme"
            >
              <div class="theme-preview">
                <div class="theme-color" style="background: ${theme.colors.primary}"></div>
                <div class="theme-color" style="background: ${theme.colors.secondary}"></div>
                <div class="theme-color" style="background: ${theme.colors.accent}"></div>
              </div>
              <span class="theme-name">${theme.name}</span>
            </button>
          `).join('')}
        </div>
      </div>
    `;

    // Add event listeners
    container.querySelectorAll('[data-theme]').forEach(button => {
      button.addEventListener('click', (e) => {
        e.preventDefault();
        const theme = button.dataset.theme;
        this.switchTheme(theme);
        
        // Update active state
        container.querySelectorAll('.theme-option').forEach(opt => {
          opt.classList.remove('active');
        });
        button.classList.add('active');
      });
    });
  }

  updateMetaThemeColor(color) {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.name = 'theme-color';
      document.head.appendChild(metaThemeColor);
    }
    metaThemeColor.content = color;
  }

  handleSystemThemeChange(e) {
    // Only auto-switch if user hasn't manually selected a theme
    const hasManualTheme = localStorage.getItem('sbo-theme');
    if (!hasManualTheme) {
      const systemTheme = e.matches ? 'dark' : 'light';
      // Apply system theme if available
      if (this.themes[systemTheme]) {
        this.applyTheme(systemTheme);
      }
    }
  }

  handleReducedMotionChange(e) {
    document.body.classList.toggle('reduce-motion', e.matches);
    
    // Dispatch event for other components
    document.dispatchEvent(new CustomEvent('reducedMotionChange', {
      detail: { reducedMotion: e.matches }
    }));
  }

  handleHighContrastChange(e) {
    document.body.classList.toggle('high-contrast', e.matches);
    
    // Apply high contrast adjustments
    if (e.matches) {
      this.applyHighContrastMode();
    } else {
      this.removeHighContrastMode();
    }
  }

  applyHighContrastMode() {
    const root = document.documentElement;
    root.style.setProperty('--color-accent', '#000000');
    root.style.setProperty('--text-primary', '#000000');
    root.style.setProperty('--border-dark', '#000000');
  }

  removeHighContrastMode() {
    // Reapply current theme
    this.applyTheme();
  }

  dispatchThemeChangeEvent(themeName) {
    document.dispatchEvent(new CustomEvent('themeChange', {
      detail: {
        theme: themeName,
        colors: this.themes[themeName].colors
      }
    }));
  }

  // Utility methods for other components
  getThemeColor(colorName) {
    return this.themes[this.currentTheme].colors[colorName] || null;
  }

  isDarkTheme() {
    // Simple heuristic: check if accent color is light
    const accentColor = this.getThemeColor('accent');
    if (!accentColor) return false;
    
    // Convert hex to RGB and calculate luminance
    const hex = accentColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    
    // Calculate relative luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    return luminance < 0.5;
  }

  addCustomTheme(key, theme) {
    this.themes[key] = theme;
  }

  removeCustomTheme(key) {
    if (this.themes[key] && key !== 'elegant') {
      delete this.themes[key];
      
      // Switch to default if current theme was removed
      if (this.currentTheme === key) {
        this.switchTheme('elegant');
      }
    }
  }
}
