/**
 * Loading Component
 * Handles loading states and indicators
 */

export class LoadingComponent {
  constructor() {
    this.loadingIndicator = null;
    this.loadingOverlay = null;
    this.activeRequests = 0;
    this.showDelay = 200; // Delay before showing loader
    this.hideDelay = 100; // Delay before hiding loader
    this.showTimer = null;
    this.hideTimer = null;
  }

  init() {
    this.findElements();
    this.setupHTMXListeners();
    this.setupGlobalListeners();
  }

  findElements() {
    this.loadingIndicator = document.getElementById('loading-indicator');
    this.loadingOverlay = document.querySelector('.loading-overlay');
  }

  setupHTMXListeners() {
    // HTMX request lifecycle
    document.addEventListener('htmx:beforeRequest', (e) => {
      this.handleRequestStart(e);
    });

    document.addEventListener('htmx:afterRequest', (e) => {
      this.handleRequestEnd(e);
    });

    document.addEventListener('htmx:sendError', (e) => {
      this.handleRequestError(e);
    });

    document.addEventListener('htmx:responseError', (e) => {
      this.handleRequestError(e);
    });

    document.addEventListener('htmx:timeout', (e) => {
      this.handleRequestTimeout(e);
    });
  }

  setupGlobalListeners() {
    // Form submissions
    document.addEventListener('submit', (e) => {
      if (!e.target.hasAttribute('hx-post') && !e.target.hasAttribute('hx-get')) {
        this.handleFormSubmit(e);
      }
    });

    // Link clicks for navigation
    document.addEventListener('click', (e) => {
      const link = e.target.closest('a[href]');
      if (link && this.shouldShowLoadingForLink(link)) {
        this.handleLinkClick(e, link);
      }
    });
  }

  handleRequestStart(e) {
    this.activeRequests++;
    
    // Add loading class to the triggering element
    if (e.detail.elt) {
      e.detail.elt.classList.add('loading');
    }
    
    this.showLoading();
  }

  handleRequestEnd(e) {
    this.activeRequests = Math.max(0, this.activeRequests - 1);
    
    // Remove loading class from the triggering element
    if (e.detail.elt) {
      e.detail.elt.classList.remove('loading');
    }
    
    if (this.activeRequests === 0) {
      this.hideLoading();
    }
  }

  handleRequestError(e) {
    this.activeRequests = Math.max(0, this.activeRequests - 1);
    
    // Remove loading class from the triggering element
    if (e.detail.elt) {
      e.detail.elt.classList.remove('loading');
    }
    
    if (this.activeRequests === 0) {
      this.hideLoading();
    }
    
    // Show error message
    this.showError('Request failed. Please try again.');
  }

  handleRequestTimeout(e) {
    this.activeRequests = Math.max(0, this.activeRequests - 1);
    
    // Remove loading class from the triggering element
    if (e.detail.elt) {
      e.detail.elt.classList.remove('loading');
    }
    
    if (this.activeRequests === 0) {
      this.hideLoading();
    }
    
    // Show timeout message
    this.showError('Request timed out. Please check your connection and try again.');
  }

  handleFormSubmit(e) {
    const form = e.target;
    
    // Skip if form has HTMX attributes (handled separately)
    if (form.hasAttribute('hx-post') || form.hasAttribute('hx-get')) {
      return;
    }
    
    // Show loading for regular form submissions
    this.showLoading();
    
    // Add loading class to submit button
    const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
    if (submitButton) {
      submitButton.classList.add('loading');
      submitButton.disabled = true;
    }
  }

  handleLinkClick(e, link) {
    // Show loading for navigation links
    this.showLoading();
    
    // Add loading class to the link
    link.classList.add('loading');
  }

  shouldShowLoadingForLink(link) {
    const href = link.getAttribute('href');
    
    // Skip for:
    // - Anchor links
    // - External links
    // - JavaScript links
    // - Download links
    if (!href || 
        href.startsWith('#') || 
        href.startsWith('javascript:') ||
        href.startsWith('mailto:') ||
        href.startsWith('tel:') ||
        link.hasAttribute('download') ||
        link.target === '_blank') {
      return false;
    }
    
    // Skip for external links
    try {
      const url = new URL(href, window.location.origin);
      if (url.origin !== window.location.origin) {
        return false;
      }
    } catch (e) {
      // Invalid URL, skip
      return false;
    }
    
    return true;
  }

  showLoading() {
    // Clear any pending hide timer
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
      this.hideTimer = null;
    }
    
    // Set show timer if not already visible
    if (!this.showTimer && !this.isVisible()) {
      this.showTimer = setTimeout(() => {
        this.displayLoading();
        this.showTimer = null;
      }, this.showDelay);
    }
  }

  hideLoading() {
    // Clear any pending show timer
    if (this.showTimer) {
      clearTimeout(this.showTimer);
      this.showTimer = null;
    }
    
    // Set hide timer
    if (!this.hideTimer) {
      this.hideTimer = setTimeout(() => {
        this.hideLoadingImmediate();
        this.hideTimer = null;
      }, this.hideDelay);
    }
  }

  displayLoading() {
    if (this.loadingIndicator) {
      this.loadingIndicator.classList.add('show');
    }
    
    // Add loading class to body
    document.body.classList.add('loading');
    
    // Dispatch event
    this.dispatchEvent('loadingShow');
  }

  hideLoadingImmediate() {
    if (this.loadingIndicator) {
      this.loadingIndicator.classList.remove('show');
    }
    
    // Remove loading class from body
    document.body.classList.remove('loading');
    
    // Remove loading classes from all elements
    document.querySelectorAll('.loading').forEach(element => {
      element.classList.remove('loading');
      if (element.tagName === 'BUTTON' || element.tagName === 'INPUT') {
        element.disabled = false;
      }
    });
    
    // Dispatch event
    this.dispatchEvent('loadingHide');
  }

  isVisible() {
    return this.loadingIndicator?.classList.contains('show') || false;
  }

  showError(message) {
    // Create error notification
    const errorElement = document.createElement('div');
    errorElement.className = 'loading-error alert alert-error';
    errorElement.innerHTML = `
      <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
      <span>${message}</span>
      <button type="button" class="alert-close" aria-label="Close">
        <i class="fas fa-times" aria-hidden="true"></i>
      </button>
    `;
    
    // Add to page
    document.body.appendChild(errorElement);
    
    // Setup close button
    const closeButton = errorElement.querySelector('.alert-close');
    closeButton.addEventListener('click', () => {
      errorElement.remove();
    });
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (errorElement.parentNode) {
        errorElement.remove();
      }
    }, 5000);
    
    // Animate in
    requestAnimationFrame(() => {
      errorElement.classList.add('show');
    });
  }

  // Public methods for manual control
  show() {
    this.activeRequests++;
    this.showLoading();
  }

  hide() {
    this.activeRequests = Math.max(0, this.activeRequests - 1);
    if (this.activeRequests === 0) {
      this.hideLoading();
    }
  }

  forceHide() {
    this.activeRequests = 0;
    this.hideLoadingImmediate();
  }

  setLoadingText(text) {
    if (this.loadingIndicator) {
      const textElement = this.loadingIndicator.querySelector('span');
      if (textElement) {
        textElement.textContent = text;
      }
    }
  }

  createOverlay() {
    if (this.loadingOverlay) return this.loadingOverlay;
    
    this.loadingOverlay = document.createElement('div');
    this.loadingOverlay.className = 'loading-overlay';
    this.loadingOverlay.innerHTML = `
      <div class="loading-spinner">
        <div class="spinner"></div>
        <span>Loading...</span>
      </div>
    `;
    
    document.body.appendChild(this.loadingOverlay);
    return this.loadingOverlay;
  }

  showOverlay(text = 'Loading...') {
    const overlay = this.createOverlay();
    const textElement = overlay.querySelector('span');
    if (textElement) {
      textElement.textContent = text;
    }
    
    overlay.classList.add('visible');
    document.body.style.overflow = 'hidden';
  }

  hideOverlay() {
    if (this.loadingOverlay) {
      this.loadingOverlay.classList.remove('visible');
      document.body.style.overflow = '';
    }
  }

  dispatchEvent(eventName, detail = {}) {
    document.dispatchEvent(new CustomEvent(eventName, {
      detail: { loading: this, ...detail }
    }));
  }

  // Cleanup method
  destroy() {
    if (this.showTimer) {
      clearTimeout(this.showTimer);
    }
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
    }
    
    this.forceHide();
    
    if (this.loadingOverlay) {
      this.loadingOverlay.remove();
    }
  }
}
