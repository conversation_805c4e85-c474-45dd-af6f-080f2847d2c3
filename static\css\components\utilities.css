/* Utility Component Styles */

/* Text utilities */
.text-shadow {
  text-shadow: 0 2px 4px var(--shadow-light);
}

.text-shadow-lg {
  text-shadow: 0 4px 8px var(--shadow-medium);
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Background utilities */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.bg-gradient-accent {
  background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-dark) 100%);
}

.bg-gradient-hero {
  background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-dark) 50%, var(--color-dark) 100%);
}

.bg-pattern {
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
  background-size: 50px 50px;
}

/* Glass effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.2);
}

/* Backdrop utilities */
.backdrop-blur {
  backdrop-filter: blur(4px);
}

.backdrop-blur-sm {
  backdrop-filter: blur(2px);
}

.backdrop-blur-lg {
  backdrop-filter: blur(8px);
}

.backdrop-blur-xl {
  backdrop-filter: blur(16px);
}

/* Shadow utilities */
.shadow-soft {
  box-shadow: 0 4px 20px var(--shadow-light);
}

.shadow-medium {
  box-shadow: 0 8px 32px var(--shadow-medium);
}

.shadow-hard {
  box-shadow: 0 12px 40px var(--shadow-dark);
}

.shadow-inner {
  box-shadow: inset 0 2px 4px var(--shadow-light);
}

.shadow-glow {
  box-shadow: 0 0 20px var(--color-accent);
}

/* Border utilities */
.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(var(--bg-white), var(--bg-white)) padding-box,
              linear-gradient(135deg, var(--color-accent), var(--color-secondary)) border-box;
}

/* Spacing utilities */
.space-y-1 > * + * {
  margin-top: var(--space-1);
}

.space-y-2 > * + * {
  margin-top: var(--space-2);
}

.space-y-3 > * + * {
  margin-top: var(--space-3);
}

.space-y-4 > * + * {
  margin-top: var(--space-4);
}

.space-y-6 > * + * {
  margin-top: var(--space-6);
}

.space-y-8 > * + * {
  margin-top: var(--space-8);
}

.space-x-1 > * + * {
  margin-left: var(--space-1);
}

.space-x-2 > * + * {
  margin-left: var(--space-2);
}

.space-x-3 > * + * {
  margin-left: var(--space-3);
}

.space-x-4 > * + * {
  margin-left: var(--space-4);
}

.space-x-6 > * + * {
  margin-left: var(--space-6);
}

/* Layout utilities */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.container-sm {
  max-width: 640px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.container-lg {
  max-width: 1536px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.section {
  padding: var(--space-16) 0;
}

.section-sm {
  padding: var(--space-12) 0;
}

.section-lg {
  padding: var(--space-24) 0;
}

/* Flexbox utilities */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* Grid utilities */
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
}

.grid-auto-fill {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--space-6);
}

/* Aspect ratio utilities */
.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.aspect-photo {
  aspect-ratio: 4 / 3;
}

/* Overflow utilities */
.overflow-hidden {
  overflow: hidden;
}

.overflow-scroll {
  overflow: auto;
}

.overflow-x-scroll {
  overflow-x: auto;
  overflow-y: hidden;
}

.overflow-y-scroll {
  overflow-x: hidden;
  overflow-y: auto;
}

/* Position utilities */
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

/* Z-index utilities */
.z-dropdown {
  z-index: var(--z-dropdown);
}

.z-sticky {
  z-index: var(--z-sticky);
}

.z-fixed {
  z-index: var(--z-fixed);
}

.z-modal {
  z-index: var(--z-modal);
}

.z-toast {
  z-index: var(--z-toast);
}

/* Visibility utilities */
.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.opacity-0 {
  opacity: 0;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-75 {
  opacity: 0.75;
}

.opacity-100 {
  opacity: 1;
}

/* Interactive utilities */
.cursor-pointer {
  cursor: pointer;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.select-none {
  user-select: none;
}

.select-all {
  user-select: all;
}

.pointer-events-none {
  pointer-events: none;
}

.pointer-events-auto {
  pointer-events: auto;
}

/* Responsive utilities */
.responsive-text {
  font-size: clamp(var(--text-sm), 2.5vw, var(--text-lg));
}

.responsive-heading {
  font-size: clamp(var(--text-2xl), 5vw, var(--text-5xl));
}

/* Table utilities */
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.table-fixed {
  table-layout: fixed;
}

/* Alert utilities */
.alert {
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-4);
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
}

.alert-success {
  background: rgba(16, 185, 129, 0.1);
  border: 1px solid rgba(16, 185, 129, 0.2);
  color: #065f46;
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
  color: #92400e;
}

.alert-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: #991b1b;
}

.alert-info {
  background: rgba(18, 52, 88, 0.1);
  border: 1px solid rgba(18, 52, 88, 0.2);
  color: var(--color-accent-dark);
}

/* Badge utilities */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  font-weight: 600;
  border-radius: var(--radius-full);
}

.badge-primary {
  background: var(--color-accent);
  color: var(--text-light);
}

.badge-secondary {
  background: var(--color-secondary);
  color: var(--color-accent);
}

.badge-success {
  background: var(--color-success);
  color: var(--text-light);
}

.badge-warning {
  background: var(--color-warning);
  color: var(--text-light);
}

.badge-error {
  background: var(--color-error);
  color: var(--text-light);
}

/* Responsive design */
@media (max-width: 768px) {
  .container,
  .container-sm,
  .container-lg {
    padding: 0 var(--space-3);
  }
  
  .section {
    padding: var(--space-12) 0;
  }
  
  .section-lg {
    padding: var(--space-16) 0;
  }
  
  .grid-auto-fit,
  .grid-auto-fill {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }
}
