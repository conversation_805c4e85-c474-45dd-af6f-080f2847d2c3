/**
 * Navigation Component
 * Handles mobile menu, scroll effects, and navigation interactions
 */

export class NavigationComponent {
  constructor() {
    this.navbar = null;
    this.mobileMenuButton = null;
    this.mobileMenu = null;
    this.isMenuOpen = false;
    this.lastScrollY = 0;
    this.scrollDirection = 'up';
  }

  init() {
    this.findElements();
    this.setupEventListeners();
    this.setupScrollEffects();
    this.setupActiveLinks();
  }

  findElements() {
    this.navbar = document.querySelector('.navbar');
    this.mobileMenuButton = document.querySelector('.mobile-menu-button');
    this.mobileMenu = document.querySelector('.mobile-nav');
    this.navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
  }

  setupEventListeners() {
    // Mobile menu toggle
    if (this.mobileMenuButton) {
      this.mobileMenuButton.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleMobileMenu();
      });
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', (e) => {
      if (this.isMenuOpen && !this.navbar.contains(e.target)) {
        this.closeMobileMenu();
      }
    });

    // Close mobile menu on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isMenuOpen) {
        this.closeMobileMenu();
      }
    });

    // Handle nav link clicks
    this.navLinks.forEach(link => {
      link.addEventListener('click', (e) => {
        this.handleNavLinkClick(e, link);
      });
    });

    // Handle window resize
    window.addEventListener('resize', () => {
      this.handleResize();
    });
  }

  setupScrollEffects() {
    if (!this.navbar) return;

    // Add scroll class for styling
    window.addEventListener('scroll', () => {
      this.handleScroll();
    });
  }

  setupActiveLinks() {
    // Set active link based on current page
    const currentPath = window.location.pathname;
    
    this.navLinks.forEach(link => {
      const href = link.getAttribute('href');
      if (href && this.isLinkActive(href, currentPath)) {
        link.classList.add('active');
      } else {
        link.classList.remove('active');
      }
    });
  }

  toggleMobileMenu() {
    if (this.isMenuOpen) {
      this.closeMobileMenu();
    } else {
      this.openMobileMenu();
    }
  }

  openMobileMenu() {
    if (!this.mobileMenu) return;

    this.isMenuOpen = true;
    this.mobileMenu.classList.add('open');
    this.mobileMenuButton?.setAttribute('aria-expanded', 'true');
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
    
    // Focus first menu item
    const firstMenuItem = this.mobileMenu.querySelector('a');
    if (firstMenuItem) {
      firstMenuItem.focus();
    }

    // Animate menu items
    this.animateMenuItems('in');

    // Dispatch event
    this.dispatchEvent('mobileMenuOpened');
  }

  closeMobileMenu() {
    if (!this.mobileMenu || !this.isMenuOpen) return;

    this.isMenuOpen = false;
    this.mobileMenu.classList.remove('open');
    this.mobileMenuButton?.setAttribute('aria-expanded', 'false');
    
    // Restore body scroll
    document.body.style.overflow = '';
    
    // Focus menu button
    if (this.mobileMenuButton) {
      this.mobileMenuButton.focus();
    }

    // Animate menu items
    this.animateMenuItems('out');

    // Dispatch event
    this.dispatchEvent('mobileMenuClosed');
  }

  animateMenuItems(direction) {
    const menuItems = this.mobileMenu?.querySelectorAll('.mobile-nav-link');
    if (!menuItems) return;

    menuItems.forEach((item, index) => {
      const delay = direction === 'in' ? index * 50 : (menuItems.length - index - 1) * 50;
      
      setTimeout(() => {
        if (direction === 'in') {
          item.style.opacity = '1';
          item.style.transform = 'translateX(0)';
        } else {
          item.style.opacity = '0';
          item.style.transform = 'translateX(-20px)';
        }
      }, delay);
    });
  }

  handleNavLinkClick(e, link) {
    const href = link.getAttribute('href');
    
    // Handle anchor links
    if (href && href.startsWith('#')) {
      e.preventDefault();
      this.scrollToSection(href);
      this.closeMobileMenu();
      return;
    }

    // Close mobile menu for regular links
    if (this.isMenuOpen) {
      this.closeMobileMenu();
    }

    // Update active state
    this.updateActiveLink(link);
  }

  scrollToSection(hash) {
    const target = document.querySelector(hash);
    if (!target) return;

    const navbarHeight = this.navbar?.offsetHeight || 0;
    const targetPosition = target.offsetTop - navbarHeight - 20;

    window.scrollTo({
      top: targetPosition,
      behavior: 'smooth'
    });

    // Update URL without triggering navigation
    history.pushState(null, null, hash);
  }

  updateActiveLink(activeLink) {
    this.navLinks.forEach(link => {
      link.classList.remove('active');
    });
    activeLink.classList.add('active');
  }

  isLinkActive(href, currentPath) {
    // Exact match
    if (href === currentPath) return true;
    
    // Home page special case
    if (href === '/' && (currentPath === '/' || currentPath === '/home/')) return true;
    
    // Partial match for sections
    if (href !== '/' && currentPath.startsWith(href)) return true;
    
    return false;
  }

  handleScroll() {
    const currentScrollY = window.pageYOffset;
    
    // Determine scroll direction
    this.scrollDirection = currentScrollY > this.lastScrollY ? 'down' : 'up';
    this.lastScrollY = currentScrollY;

    // Add/remove scrolled class
    if (currentScrollY > 50) {
      this.navbar?.classList.add('scrolled');
    } else {
      this.navbar?.classList.remove('scrolled');
    }

    // Hide/show navbar on scroll (optional)
    if (this.shouldHideOnScroll()) {
      if (this.scrollDirection === 'down' && currentScrollY > 200) {
        this.navbar?.classList.add('hidden');
      } else {
        this.navbar?.classList.remove('hidden');
      }
    }

    // Update scroll progress
    this.updateScrollProgress();
  }

  shouldHideOnScroll() {
    // Only hide on scroll for certain pages or when enabled
    return this.navbar?.dataset.hideOnScroll === 'true';
  }

  updateScrollProgress() {
    const progressBar = document.querySelector('.scroll-progress');
    if (!progressBar) return;

    const windowHeight = window.innerHeight;
    const documentHeight = document.documentElement.scrollHeight - windowHeight;
    const scrolled = (window.pageYOffset / documentHeight) * 100;
    
    progressBar.style.width = `${Math.min(scrolled, 100)}%`;
  }

  handleResize() {
    // Close mobile menu on resize to desktop
    if (window.innerWidth >= 768 && this.isMenuOpen) {
      this.closeMobileMenu();
    }

    // Reset menu item styles
    const menuItems = this.mobileMenu?.querySelectorAll('.mobile-nav-link');
    if (menuItems && window.innerWidth >= 768) {
      menuItems.forEach(item => {
        item.style.opacity = '';
        item.style.transform = '';
      });
    }
  }

  // Public methods for external use
  showNavbar() {
    this.navbar?.classList.remove('hidden');
  }

  hideNavbar() {
    this.navbar?.classList.add('hidden');
  }

  setActiveLink(href) {
    this.navLinks.forEach(link => {
      if (link.getAttribute('href') === href) {
        link.classList.add('active');
      } else {
        link.classList.remove('active');
      }
    });
  }

  addNavLink(text, href, position = 'end') {
    const desktopNav = document.querySelector('.navbar-nav.desktop');
    const mobileNav = document.querySelector('.mobile-nav-links');
    
    if (desktopNav) {
      const link = this.createNavLink(text, href, 'nav-link');
      if (position === 'start') {
        desktopNav.insertBefore(link, desktopNav.firstChild);
      } else {
        desktopNav.appendChild(link);
      }
    }
    
    if (mobileNav) {
      const link = this.createNavLink(text, href, 'mobile-nav-link');
      if (position === 'start') {
        mobileNav.insertBefore(link, mobileNav.firstChild);
      } else {
        mobileNav.appendChild(link);
      }
    }
  }

  createNavLink(text, href, className) {
    const link = document.createElement('a');
    link.href = href;
    link.textContent = text;
    link.className = className;
    
    link.addEventListener('click', (e) => {
      this.handleNavLinkClick(e, link);
    });
    
    return link;
  }

  dispatchEvent(eventName, detail = {}) {
    document.dispatchEvent(new CustomEvent(eventName, {
      detail: { navigation: this, ...detail }
    }));
  }

  // Cleanup method
  destroy() {
    this.closeMobileMenu();
    // Remove event listeners if needed
  }
}
