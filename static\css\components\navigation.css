/* Navigation Component Styles */

.navbar {
  background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-dark) 100%);
  box-shadow: 0 4px 20px var(--shadow-medium);
  position: relative;
  z-index: var(--z-sticky);
}

.navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
  pointer-events: none;
}

.navbar-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 4rem;
  position: relative;
  z-index: 1;
}

/* Logo and brand */
.navbar-brand {
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: transform var(--transition-fast);
}

.navbar-brand:hover {
  transform: translateY(-1px);
}

.navbar-logo {
  width: 2.5rem;
  height: 2.5rem;
  margin-right: var(--space-3);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: 0 2px 8px var(--shadow-light);
}

.navbar-logo img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.navbar-title {
  color: var(--text-light);
}

.navbar-title h1 {
  font-size: var(--text-lg);
  font-weight: 700;
  line-height: var(--leading-tight);
  margin: 0;
}

.navbar-subtitle {
  font-size: var(--text-xs);
  color: var(--color-secondary);
  margin: 0;
}

/* Desktop navigation */
.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.navbar-nav.desktop {
  display: none;
}

.nav-link {
  color: var(--color-secondary);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left var(--transition-normal);
}

.nav-link:hover {
  color: var(--text-light);
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.nav-link:hover::before {
  left: 100%;
}

.nav-link.active {
  color: var(--text-light);
  background-color: rgba(255, 255, 255, 0.15);
}

/* Auth links */
.navbar-auth {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.auth-link {
  color: var(--color-secondary);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
}

.auth-link:hover {
  color: var(--text-light);
  border-color: rgba(255, 255, 255, 0.2);
  background-color: rgba(255, 255, 255, 0.1);
}

.auth-link.primary {
  background-color: var(--color-secondary);
  color: var(--color-accent);
  border-color: var(--color-secondary);
}

.auth-link.primary:hover {
  background-color: var(--color-primary);
  color: var(--color-accent-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-medium);
}

/* Mobile menu button */
.mobile-menu-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  color: var(--color-secondary);
  border: none;
  background: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.mobile-menu-button:hover {
  color: var(--text-light);
  background-color: rgba(255, 255, 255, 0.1);
}

.mobile-menu-button svg {
  width: 1.5rem;
  height: 1.5rem;
}

/* Mobile navigation */
.mobile-nav {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--color-accent-dark);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px var(--shadow-dark);
  transform: translateY(-10px);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.mobile-nav.open {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-nav-content {
  padding: var(--space-4);
}

.mobile-nav-links {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.mobile-nav-link {
  color: var(--color-secondary);
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-lg);
  font-size: var(--text-base);
  font-weight: 500;
  transition: all var(--transition-fast);
  display: block;
}

.mobile-nav-link:hover {
  color: var(--text-light);
  background-color: rgba(255, 255, 255, 0.1);
}

.mobile-nav-auth {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  padding-top: var(--space-4);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive design */
@media (min-width: 768px) {
  .navbar-nav.desktop {
    display: flex;
  }
  
  .mobile-menu-button {
    display: none;
  }
  
  .mobile-nav {
    display: none;
  }
}

/* Loading indicator in navbar */
.navbar-loading {
  position: absolute;
  top: 0;
  right: var(--space-4);
  background: var(--color-secondary);
  color: var(--color-accent);
  padding: var(--space-2) var(--space-4);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  opacity: 0;
  transform: translateY(-100%);
  transition: all var(--transition-normal);
  z-index: var(--z-toast);
}

.navbar-loading.show {
  opacity: 1;
  transform: translateY(0);
}

.navbar-loading .spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--color-accent-light);
  border-top: 2px solid var(--color-accent);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
