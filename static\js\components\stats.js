/**
 * Stats Component
 * Handles animated counters and statistics display
 */

export class StatsComponent {
  constructor() {
    this.counters = [];
    this.observer = null;
    this.animatedCounters = new Set();
  }

  init() {
    this.findCounters();
    this.setupIntersectionObserver();
    this.observeCounters();
  }

  findCounters() {
    this.counters = document.querySelectorAll('[data-count]');
  }

  setupIntersectionObserver() {
    this.observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting && !this.animatedCounters.has(entry.target)) {
          this.animateCounter(entry.target);
        }
      });
    }, {
      threshold: 0.5,
      rootMargin: '0px 0px -100px 0px'
    });
  }

  observeCounters() {
    this.counters.forEach(counter => {
      this.observer.observe(counter);
    });
  }

  animateCounter(element) {
    if (this.animatedCounters.has(element)) return;

    const target = parseInt(element.dataset.count) || 0;
    const duration = parseInt(element.dataset.duration) || 2000;
    const easing = element.dataset.easing || 'easeOutCubic';
    
    this.animatedCounters.add(element);
    
    // Check for reduced motion preference
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      element.textContent = target.toLocaleString();
      this.dispatchCounterEvent(element, target);
      return;
    }

    this.startCounterAnimation(element, target, duration, easing);
  }

  startCounterAnimation(element, target, duration, easing) {
    const startTime = performance.now();
    const startValue = 0;
    
    const animate = (currentTime) => {
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      // Apply easing function
      const easedProgress = this.applyEasing(progress, easing);
      const currentValue = Math.floor(startValue + (target - startValue) * easedProgress);
      
      // Update display
      element.textContent = currentValue.toLocaleString();
      
      // Continue animation or finish
      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        element.textContent = target.toLocaleString();
        this.dispatchCounterEvent(element, target);
      }
    };
    
    requestAnimationFrame(animate);
  }

  applyEasing(t, easingType) {
    switch (easingType) {
      case 'linear':
        return t;
      case 'easeInQuad':
        return t * t;
      case 'easeOutQuad':
        return t * (2 - t);
      case 'easeInOutQuad':
        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
      case 'easeInCubic':
        return t * t * t;
      case 'easeOutCubic':
        return (--t) * t * t + 1;
      case 'easeInOutCubic':
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
      case 'easeInQuart':
        return t * t * t * t;
      case 'easeOutQuart':
        return 1 - (--t) * t * t * t;
      case 'easeInOutQuart':
        return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * (--t) * t * t * t;
      case 'easeInQuint':
        return t * t * t * t * t;
      case 'easeOutQuint':
        return 1 + (--t) * t * t * t * t;
      case 'easeInOutQuint':
        return t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * (--t) * t * t * t * t;
      default:
        return this.applyEasing(t, 'easeOutCubic');
    }
  }

  dispatchCounterEvent(element, finalValue) {
    element.dispatchEvent(new CustomEvent('counterComplete', {
      detail: {
        element,
        value: finalValue
      }
    }));
  }

  // Public methods for manual control
  animateAllCounters() {
    this.counters.forEach(counter => {
      if (!this.animatedCounters.has(counter)) {
        this.animateCounter(counter);
      }
    });
  }

  resetCounter(element) {
    if (this.animatedCounters.has(element)) {
      this.animatedCounters.delete(element);
      element.textContent = '0';
    }
  }

  resetAllCounters() {
    this.animatedCounters.clear();
    this.counters.forEach(counter => {
      counter.textContent = '0';
    });
  }

  updateCounterValue(element, newValue) {
    element.dataset.count = newValue;
    this.resetCounter(element);
    this.animateCounter(element);
  }

  addCounter(element) {
    if (!this.counters.includes(element)) {
      this.counters.push(element);
      this.observer.observe(element);
    }
  }

  removeCounter(element) {
    const index = this.counters.indexOf(element);
    if (index > -1) {
      this.counters.splice(index, 1);
      this.observer.unobserve(element);
      this.animatedCounters.delete(element);
    }
  }

  // Utility methods
  formatNumber(number, options = {}) {
    const defaults = {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    };
    
    const formatOptions = { ...defaults, ...options };
    
    try {
      return new Intl.NumberFormat('en-US', formatOptions).format(number);
    } catch (e) {
      return number.toLocaleString();
    }
  }

  // Cleanup method
  destroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
    this.counters = [];
    this.animatedCounters.clear();
  }

  // Reinitialize for new content
  reinitialize() {
    this.findCounters();
    this.observeCounters();
  }
}
