/**
 * Search Component
 * Handles search functionality and filters
 */

export class SearchComponent {
  constructor() {
    this.searchForm = null;
    this.searchInput = null;
    this.searchButton = null;
    this.filters = [];
    this.debounceTimer = null;
    this.searchHistory = [];
    this.maxHistoryItems = 10;
  }

  init() {
    this.findElements();
    this.setupEventListeners();
    this.loadSearchHistory();
    this.setupAutoComplete();
  }

  findElements() {
    this.searchForm = document.querySelector('.search-form');
    this.searchInput = document.querySelector('.search-input, input[name="search"]');
    this.searchButton = document.querySelector('.search-button');
    this.filters = document.querySelectorAll('.filter-select');
  }

  setupEventListeners() {
    if (!this.searchForm) return;

    // Form submission
    this.searchForm.addEventListener('submit', (e) => {
      this.handleFormSubmit(e);
    });

    // Search input events
    if (this.searchInput) {
      this.searchInput.addEventListener('input', (e) => {
        this.handleSearchInput(e);
      });

      this.searchInput.addEventListener('focus', (e) => {
        this.handleSearchFocus(e);
      });

      this.searchInput.addEventListener('blur', (e) => {
        this.handleSearchBlur(e);
      });

      this.searchInput.addEventListener('keydown', (e) => {
        this.handleSearchKeydown(e);
      });
    }

    // Filter changes
    this.filters.forEach(filter => {
      filter.addEventListener('change', (e) => {
        this.handleFilterChange(e);
      });
    });

    // Search button
    if (this.searchButton) {
      this.searchButton.addEventListener('click', (e) => {
        this.handleSearchButtonClick(e);
      });
    }
  }

  handleFormSubmit(e) {
    const query = this.searchInput?.value.trim();
    
    if (!query) {
      e.preventDefault();
      this.showError('Please enter a search term');
      return;
    }

    // Add to search history
    this.addToSearchHistory(query);
    
    // Show loading state
    this.setLoadingState(true);
    
    // Let the form submit naturally or handle with HTMX
    if (this.searchForm.hasAttribute('hx-get')) {
      // HTMX will handle the submission
      return;
    }
  }

  handleSearchInput(e) {
    const query = e.target.value.trim();
    
    // Clear previous debounce timer
    clearTimeout(this.debounceTimer);
    
    // Debounce search suggestions
    this.debounceTimer = setTimeout(() => {
      if (query.length >= 2) {
        this.showSearchSuggestions(query);
      } else {
        this.hideSearchSuggestions();
      }
    }, 300);
  }

  handleSearchFocus(e) {
    const query = e.target.value.trim();
    
    if (query.length >= 2) {
      this.showSearchSuggestions(query);
    } else if (this.searchHistory.length > 0) {
      this.showSearchHistory();
    }
    
    // Add focus styling
    e.target.parentElement?.classList.add('focused');
  }

  handleSearchBlur(e) {
    // Remove focus styling
    e.target.parentElement?.classList.remove('focused');
    
    // Hide suggestions after a delay to allow clicking
    setTimeout(() => {
      this.hideSearchSuggestions();
    }, 200);
  }

  handleSearchKeydown(e) {
    const suggestions = document.querySelector('.search-suggestions');
    if (!suggestions) return;

    const items = suggestions.querySelectorAll('.suggestion-item');
    const activeItem = suggestions.querySelector('.suggestion-item.active');
    
    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        this.navigateSuggestions(items, activeItem, 'down');
        break;
      case 'ArrowUp':
        e.preventDefault();
        this.navigateSuggestions(items, activeItem, 'up');
        break;
      case 'Enter':
        if (activeItem) {
          e.preventDefault();
          this.selectSuggestion(activeItem);
        }
        break;
      case 'Escape':
        this.hideSearchSuggestions();
        e.target.blur();
        break;
    }
  }

  handleFilterChange(e) {
    // Auto-submit form when filters change
    if (this.searchForm.hasAttribute('hx-get')) {
      // Trigger HTMX request
      htmx.trigger(this.searchForm, 'submit');
    } else {
      // Submit form normally
      this.searchForm.submit();
    }
  }

  handleSearchButtonClick(e) {
    if (!this.searchInput?.value.trim()) {
      e.preventDefault();
      this.searchInput?.focus();
      this.showError('Please enter a search term');
    }
  }

  showSearchSuggestions(query) {
    // Create or update suggestions dropdown
    let suggestions = document.querySelector('.search-suggestions');
    
    if (!suggestions) {
      suggestions = this.createSuggestionsDropdown();
    }
    
    // Fetch suggestions (mock data for now)
    const mockSuggestions = this.getMockSuggestions(query);
    this.renderSuggestions(suggestions, mockSuggestions);
    
    suggestions.classList.add('visible');
  }

  showSearchHistory() {
    let suggestions = document.querySelector('.search-suggestions');
    
    if (!suggestions) {
      suggestions = this.createSuggestionsDropdown();
    }
    
    this.renderSearchHistory(suggestions);
    suggestions.classList.add('visible');
  }

  hideSearchSuggestions() {
    const suggestions = document.querySelector('.search-suggestions');
    if (suggestions) {
      suggestions.classList.remove('visible');
    }
  }

  createSuggestionsDropdown() {
    const suggestions = document.createElement('div');
    suggestions.className = 'search-suggestions';
    suggestions.innerHTML = '<div class="suggestions-content"></div>';
    
    // Position relative to search input
    if (this.searchInput) {
      this.searchInput.parentElement.appendChild(suggestions);
    }
    
    return suggestions;
  }

  renderSuggestions(container, suggestions) {
    const content = container.querySelector('.suggestions-content');
    
    content.innerHTML = suggestions.map(suggestion => `
      <div class="suggestion-item" data-value="${suggestion.value}">
        <i class="fas fa-search suggestion-icon" aria-hidden="true"></i>
        <span class="suggestion-text">${suggestion.text}</span>
        <span class="suggestion-type">${suggestion.type}</span>
      </div>
    `).join('');
    
    // Add click listeners
    content.querySelectorAll('.suggestion-item').forEach(item => {
      item.addEventListener('click', () => {
        this.selectSuggestion(item);
      });
    });
  }

  renderSearchHistory(container) {
    const content = container.querySelector('.suggestions-content');
    
    if (this.searchHistory.length === 0) {
      content.innerHTML = '<div class="no-suggestions">No recent searches</div>';
      return;
    }
    
    content.innerHTML = `
      <div class="suggestions-header">Recent Searches</div>
      ${this.searchHistory.map(item => `
        <div class="suggestion-item history-item" data-value="${item}">
          <i class="fas fa-history suggestion-icon" aria-hidden="true"></i>
          <span class="suggestion-text">${item}</span>
          <button class="remove-history" data-value="${item}" aria-label="Remove from history">
            <i class="fas fa-times" aria-hidden="true"></i>
          </button>
        </div>
      `).join('')}
    `;
    
    // Add event listeners
    content.querySelectorAll('.suggestion-item').forEach(item => {
      item.addEventListener('click', () => {
        this.selectSuggestion(item);
      });
    });
    
    content.querySelectorAll('.remove-history').forEach(button => {
      button.addEventListener('click', (e) => {
        e.stopPropagation();
        this.removeFromSearchHistory(button.dataset.value);
      });
    });
  }

  navigateSuggestions(items, activeItem, direction) {
    if (items.length === 0) return;
    
    // Remove current active state
    if (activeItem) {
      activeItem.classList.remove('active');
    }
    
    let nextIndex = 0;
    
    if (activeItem) {
      const currentIndex = Array.from(items).indexOf(activeItem);
      if (direction === 'down') {
        nextIndex = (currentIndex + 1) % items.length;
      } else {
        nextIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
      }
    }
    
    items[nextIndex].classList.add('active');
    items[nextIndex].scrollIntoView({ block: 'nearest' });
  }

  selectSuggestion(item) {
    const value = item.dataset.value;
    
    if (this.searchInput) {
      this.searchInput.value = value;
      this.searchInput.focus();
    }
    
    this.hideSearchSuggestions();
    
    // Trigger search
    if (this.searchForm) {
      this.searchForm.dispatchEvent(new Event('submit'));
    }
  }

  getMockSuggestions(query) {
    // Mock suggestions - replace with actual API call
    const mockData = [
      { value: 'ordinance 2024', text: 'Ordinance 2024', type: 'ordinance' },
      { value: 'traffic regulations', text: 'Traffic Regulations', type: 'category' },
      { value: 'business permits', text: 'Business Permits', type: 'category' },
      { value: 'environmental protection', text: 'Environmental Protection', type: 'category' }
    ];
    
    return mockData.filter(item => 
      item.text.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 5);
  }

  addToSearchHistory(query) {
    // Remove if already exists
    this.searchHistory = this.searchHistory.filter(item => item !== query);
    
    // Add to beginning
    this.searchHistory.unshift(query);
    
    // Limit history size
    this.searchHistory = this.searchHistory.slice(0, this.maxHistoryItems);
    
    // Save to localStorage
    this.saveSearchHistory();
  }

  removeFromSearchHistory(query) {
    this.searchHistory = this.searchHistory.filter(item => item !== query);
    this.saveSearchHistory();
    
    // Update display
    if (document.querySelector('.search-suggestions.visible')) {
      this.showSearchHistory();
    }
  }

  loadSearchHistory() {
    try {
      const saved = localStorage.getItem('sbo-search-history');
      if (saved) {
        this.searchHistory = JSON.parse(saved);
      }
    } catch (e) {
      console.warn('Failed to load search history:', e);
      this.searchHistory = [];
    }
  }

  saveSearchHistory() {
    try {
      localStorage.setItem('sbo-search-history', JSON.stringify(this.searchHistory));
    } catch (e) {
      console.warn('Failed to save search history:', e);
    }
  }

  setupAutoComplete() {
    // Setup autocomplete functionality if needed
    if (this.searchInput) {
      this.searchInput.setAttribute('autocomplete', 'off');
    }
  }

  setLoadingState(loading) {
    if (this.searchButton) {
      if (loading) {
        this.searchButton.classList.add('loading');
        this.searchButton.disabled = true;
      } else {
        this.searchButton.classList.remove('loading');
        this.searchButton.disabled = false;
      }
    }
  }

  showError(message) {
    // Create or update error message
    let errorElement = document.querySelector('.search-error');
    
    if (!errorElement) {
      errorElement = document.createElement('div');
      errorElement.className = 'search-error alert alert-error';
      this.searchForm?.appendChild(errorElement);
    }
    
    errorElement.innerHTML = `
      <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
      <span>${message}</span>
    `;
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
      errorElement.remove();
    }, 3000);
  }

  // Public methods
  clearSearch() {
    if (this.searchInput) {
      this.searchInput.value = '';
      this.searchInput.focus();
    }
    this.hideSearchSuggestions();
  }

  setSearchQuery(query) {
    if (this.searchInput) {
      this.searchInput.value = query;
    }
  }

  getSearchQuery() {
    return this.searchInput?.value.trim() || '';
  }
}
