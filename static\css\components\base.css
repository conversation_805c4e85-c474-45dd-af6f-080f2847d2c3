/* Base Styles */

/* Reset and base typography */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-heading);
  font-weight: 600;
  line-height: var(--leading-tight);
  color: var(--text-primary);
  margin: 0 0 var(--space-4) 0;
}

h1 {
  font-size: var(--text-4xl);
  font-weight: 700;
}

h2 {
  font-size: var(--text-3xl);
}

h3 {
  font-size: var(--text-2xl);
}

h4 {
  font-size: var(--text-xl);
}

h5 {
  font-size: var(--text-lg);
}

h6 {
  font-size: var(--text-base);
}

/* Paragraphs and text */
p {
  margin: 0 0 var(--space-4) 0;
  color: var(--text-primary);
}

/* Links */
a {
  color: var(--color-accent);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--color-accent-light);
}

a:focus {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

/* Lists */
ul, ol {
  margin: 0 0 var(--space-4) 0;
  padding-left: var(--space-6);
}

li {
  margin-bottom: var(--space-1);
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Tables */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: var(--space-6);
}

th, td {
  padding: var(--space-3) var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--border-light);
}

th {
  font-weight: 600;
  color: var(--text-primary);
  background-color: var(--bg-secondary);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-accent);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-accent-dark);
}

/* Selection */
::selection {
  background-color: var(--color-accent);
  color: var(--text-light);
}

::-moz-selection {
  background-color: var(--color-accent);
  color: var(--text-light);
}

/* Focus styles for accessibility */
.focus-ring:focus {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break-before {
    page-break-before: always;
  }
  
  .print-break-after {
    page-break-after: always;
  }
  
  /* Hide navigation and footer when printing */
  nav, footer, .htmx-indicator {
    display: none !important;
  }
  
  /* Ensure content is readable when printed */
  body {
    font-size: 12pt;
    line-height: 1.4;
    color: black;
    background: white;
  }
  
  /* Expand content for print */
  .max-w-4xl, .max-w-7xl {
    max-width: none;
    margin: 0;
    padding: 0;
  }
  
  /* Print-friendly colors */
  * {
    color: black !important;
    background: white !important;
  }
}

/* Responsive typography */
@media (max-width: 640px) {
  html {
    font-size: 14px;
  }
  
  h1 {
    font-size: var(--text-3xl);
  }
  
  h2 {
    font-size: var(--text-2xl);
  }
  
  h3 {
    font-size: var(--text-xl);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  body {
    background-color: white;
    color: black;
  }
  
  a {
    color: black;
    text-decoration: underline;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
  
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
