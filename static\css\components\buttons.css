/* Button Component Styles */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  font-weight: 600;
  line-height: 1;
  text-decoration: none;
  border: 2px solid transparent;
  border-radius: var(--radius-xl);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  user-select: none;
  white-space: nowrap;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-normal);
}

.btn:hover::before {
  left: 100%;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(18, 52, 88, 0.2);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.btn:disabled::before {
  display: none;
}

/* Button variants */
.btn-primary {
  background: var(--color-accent);
  color: var(--text-light);
  border-color: var(--color-accent);
}

.btn-primary:hover {
  background: var(--color-accent-dark);
  border-color: var(--color-accent-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-medium);
}

.btn-secondary {
  background: var(--color-secondary);
  color: var(--color-accent);
  border-color: var(--color-secondary);
}

.btn-secondary:hover {
  background: var(--color-secondary-dark);
  border-color: var(--color-secondary-dark);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-light);
}

.btn-outline {
  background: transparent;
  color: var(--color-accent);
  border-color: var(--color-accent);
}

.btn-outline:hover {
  background: var(--color-accent);
  color: var(--text-light);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--shadow-medium);
}

.btn-ghost {
  background: transparent;
  color: var(--color-accent);
  border-color: transparent;
}

.btn-ghost:hover {
  background: rgba(18, 52, 88, 0.1);
  border-color: var(--color-accent-light);
  transform: translateY(-1px);
}

.btn-light {
  background: var(--bg-white);
  color: var(--color-accent);
  border-color: var(--bg-white);
  box-shadow: 0 4px 20px var(--shadow-light);
}

.btn-light:hover {
  background: var(--bg-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px var(--shadow-medium);
}

.btn-dark {
  background: var(--color-dark);
  color: var(--text-light);
  border-color: var(--color-dark);
}

.btn-dark:hover {
  background: #1a1a1a;
  border-color: #1a1a1a;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Status buttons */
.btn-success {
  background: var(--color-success);
  color: var(--text-light);
  border-color: var(--color-success);
}

.btn-success:hover {
  background: #059669;
  border-color: #059669;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);
}

.btn-warning {
  background: var(--color-warning);
  color: var(--text-light);
  border-color: var(--color-warning);
}

.btn-warning:hover {
  background: #d97706;
  border-color: #d97706;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);
}

.btn-error {
  background: var(--color-error);
  color: var(--text-light);
  border-color: var(--color-error);
}

.btn-error:hover {
  background: #dc2626;
  border-color: #dc2626;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

/* Button sizes */
.btn-xs {
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  border-radius: var(--radius-lg);
}

.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
  border-radius: var(--radius-lg);
}

.btn-lg {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
  border-radius: var(--radius-2xl);
}

.btn-xl {
  padding: var(--space-5) var(--space-10);
  font-size: var(--text-xl);
  border-radius: var(--radius-2xl);
}

/* Button states */
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1rem;
  height: 1rem;
  border: 2px solid currentColor;
  border-top: 2px solid transparent;
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

/* Icon buttons */
.btn-icon {
  padding: var(--space-3);
  border-radius: var(--radius-full);
  aspect-ratio: 1;
}

.btn-icon.btn-sm {
  padding: var(--space-2);
}

.btn-icon.btn-lg {
  padding: var(--space-4);
}

/* Button groups */
.btn-group {
  display: inline-flex;
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: 0 4px 20px var(--shadow-light);
}

.btn-group .btn {
  border-radius: 0;
  border-right-width: 1px;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--radius-xl);
  border-bottom-left-radius: var(--radius-xl);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--radius-xl);
  border-bottom-right-radius: var(--radius-xl);
  border-right-width: 2px;
}

.btn-group .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}

/* Floating action button */
.btn-fab {
  position: fixed;
  bottom: var(--space-6);
  right: var(--space-6);
  width: 3.5rem;
  height: 3.5rem;
  border-radius: var(--radius-full);
  background: var(--color-accent);
  color: var(--text-light);
  border: none;
  box-shadow: 0 8px 32px var(--shadow-dark);
  z-index: var(--z-fixed);
  transition: all var(--transition-normal);
}

.btn-fab:hover {
  transform: translateY(-4px) scale(1.1);
  box-shadow: 0 12px 40px var(--shadow-dark);
}

/* Glass button effect */
.btn-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--text-light);
}

.btn-glass:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px var(--shadow-medium);
}

/* Gradient buttons */
.btn-gradient {
  background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-dark) 100%);
  color: var(--text-light);
  border: none;
}

.btn-gradient:hover {
  background: linear-gradient(135deg, var(--color-accent-dark) 0%, var(--color-accent) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px var(--shadow-dark);
}

/* Responsive design */
@media (max-width: 768px) {
  .btn {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
  }
  
  .btn-lg {
    padding: var(--space-3) var(--space-6);
    font-size: var(--text-base);
  }
  
  .btn-xl {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
  }
  
  .btn-fab {
    bottom: var(--space-4);
    right: var(--space-4);
    width: 3rem;
    height: 3rem;
  }
  
  .btn-group {
    flex-direction: column;
  }
  
  .btn-group .btn {
    border-right-width: 2px;
    border-bottom-width: 1px;
  }
  
  .btn-group .btn:first-child {
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  }
  
  .btn-group .btn:last-child {
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
    border-bottom-width: 2px;
  }
}

/* Animation keyframes */
@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}
