/**
 * Form Handler Utility
 * Handles form validation, submission, and interactions
 */

export class FormHandler {
  constructor() {
    this.forms = [];
    this.validators = new Map();
    this.defaultRules = {
      required: (value) => value.trim() !== '',
      email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
      url: (value) => /^https?:\/\/.+/.test(value),
      number: (value) => !isNaN(value) && isFinite(value),
      minLength: (value, min) => value.length >= min,
      maxLength: (value, max) => value.length <= max,
      pattern: (value, pattern) => new RegExp(pattern).test(value)
    };
  }

  init() {
    this.findForms();
    this.setupEventListeners();
    this.setupValidation();
    this.setupAlertHandlers();
  }

  findForms() {
    this.forms = document.querySelectorAll('form');
  }

  setupEventListeners() {
    this.forms.forEach(form => {
      this.setupFormListeners(form);
    });

    // Global form event delegation
    document.addEventListener('submit', (e) => {
      this.handleFormSubmit(e);
    });

    document.addEventListener('input', (e) => {
      if (e.target.matches('input, textarea, select')) {
        this.handleFieldInput(e);
      }
    });

    document.addEventListener('blur', (e) => {
      if (e.target.matches('input, textarea, select')) {
        this.handleFieldBlur(e);
      }
    }, true);

    document.addEventListener('focus', (e) => {
      if (e.target.matches('input, textarea, select')) {
        this.handleFieldFocus(e);
      }
    }, true);
  }

  setupFormListeners(form) {
    // Real-time validation
    const fields = form.querySelectorAll('input, textarea, select');
    fields.forEach(field => {
      field.addEventListener('input', () => {
        this.validateField(field);
      });

      field.addEventListener('blur', () => {
        this.validateField(field);
      });
    });

    // Form submission
    form.addEventListener('submit', (e) => {
      if (!this.validateForm(form)) {
        e.preventDefault();
      }
    });
  }

  setupValidation() {
    // Setup custom validation rules
    this.addValidationRule('phone', (value) => {
      return /^[\+]?[1-9][\d]{0,15}$/.test(value.replace(/\s/g, ''));
    });

    this.addValidationRule('strongPassword', (value) => {
      return value.length >= 8 && 
             /[A-Z]/.test(value) && 
             /[a-z]/.test(value) && 
             /[0-9]/.test(value);
    });
  }

  setupAlertHandlers() {
    // Setup alert close buttons
    document.addEventListener('click', (e) => {
      if (e.target.matches('[data-alert-close]')) {
        const alert = e.target.closest('[data-alert]');
        if (alert) {
          this.closeAlert(alert);
        }
      }
    });

    // Auto-close alerts after timeout
    document.querySelectorAll('[data-alert][data-timeout]').forEach(alert => {
      const timeout = parseInt(alert.dataset.timeout) || 5000;
      setTimeout(() => {
        this.closeAlert(alert);
      }, timeout);
    });
  }

  handleFormSubmit(e) {
    const form = e.target;
    
    // Skip if form has HTMX attributes
    if (form.hasAttribute('hx-post') || form.hasAttribute('hx-get')) {
      return;
    }

    // Validate form
    if (!this.validateForm(form)) {
      e.preventDefault();
      return;
    }

    // Add loading state
    this.setFormLoading(form, true);
  }

  handleFieldInput(e) {
    const field = e.target;
    
    // Clear previous error state
    this.clearFieldError(field);
    
    // Real-time validation for certain field types
    if (field.type === 'email' || field.hasAttribute('data-validate-realtime')) {
      this.validateField(field);
    }
  }

  handleFieldBlur(e) {
    const field = e.target;
    this.validateField(field);
  }

  handleFieldFocus(e) {
    const field = e.target;
    this.clearFieldError(field);
  }

  validateForm(form) {
    const fields = form.querySelectorAll('input, textarea, select');
    let isValid = true;
    let firstInvalidField = null;

    fields.forEach(field => {
      if (!this.validateField(field)) {
        isValid = false;
        if (!firstInvalidField) {
          firstInvalidField = field;
        }
      }
    });

    // Focus first invalid field
    if (firstInvalidField) {
      firstInvalidField.focus();
    }

    return isValid;
  }

  validateField(field) {
    const rules = this.getFieldRules(field);
    const value = field.value;
    let isValid = true;
    let errorMessage = '';

    for (const rule of rules) {
      const result = this.applyValidationRule(value, rule);
      if (!result.valid) {
        isValid = false;
        errorMessage = result.message;
        break;
      }
    }

    if (isValid) {
      this.clearFieldError(field);
    } else {
      this.showFieldError(field, errorMessage);
    }

    return isValid;
  }

  getFieldRules(field) {
    const rules = [];

    // Required validation
    if (field.hasAttribute('required')) {
      rules.push({
        type: 'required',
        message: 'This field is required'
      });
    }

    // Type-based validation
    if (field.type === 'email') {
      rules.push({
        type: 'email',
        message: 'Please enter a valid email address'
      });
    }

    if (field.type === 'url') {
      rules.push({
        type: 'url',
        message: 'Please enter a valid URL'
      });
    }

    if (field.type === 'number') {
      rules.push({
        type: 'number',
        message: 'Please enter a valid number'
      });
    }

    // Length validation
    if (field.hasAttribute('minlength')) {
      rules.push({
        type: 'minLength',
        value: parseInt(field.getAttribute('minlength')),
        message: `Minimum length is ${field.getAttribute('minlength')} characters`
      });
    }

    if (field.hasAttribute('maxlength')) {
      rules.push({
        type: 'maxLength',
        value: parseInt(field.getAttribute('maxlength')),
        message: `Maximum length is ${field.getAttribute('maxlength')} characters`
      });
    }

    // Pattern validation
    if (field.hasAttribute('pattern')) {
      rules.push({
        type: 'pattern',
        value: field.getAttribute('pattern'),
        message: field.getAttribute('title') || 'Please match the required format'
      });
    }

    // Custom validation
    const customRules = field.getAttribute('data-validate');
    if (customRules) {
      customRules.split(',').forEach(ruleName => {
        const trimmedRule = ruleName.trim();
        if (this.validators.has(trimmedRule)) {
          rules.push({
            type: trimmedRule,
            message: field.getAttribute(`data-${trimmedRule}-message`) || `Invalid ${trimmedRule}`
          });
        }
      });
    }

    return rules;
  }

  applyValidationRule(value, rule) {
    const validator = this.defaultRules[rule.type] || this.validators.get(rule.type);
    
    if (!validator) {
      return { valid: true };
    }

    try {
      const isValid = rule.value !== undefined ? 
        validator(value, rule.value) : 
        validator(value);
      
      return {
        valid: isValid,
        message: isValid ? '' : rule.message
      };
    } catch (e) {
      console.warn('Validation error:', e);
      return { valid: true };
    }
  }

  showFieldError(field, message) {
    field.classList.add('error');
    
    // Remove existing error message
    this.clearFieldError(field, false);
    
    // Create error message element
    const errorElement = document.createElement('div');
    errorElement.className = 'form-error';
    errorElement.innerHTML = `
      <i class="fas fa-exclamation-circle" aria-hidden="true"></i>
      <span>${message}</span>
    `;
    
    // Insert after field or field group
    const insertAfter = field.closest('.form-group') || field;
    insertAfter.parentNode.insertBefore(errorElement, insertAfter.nextSibling);
    
    // Set ARIA attributes
    const errorId = `error-${field.name || field.id || Date.now()}`;
    errorElement.id = errorId;
    field.setAttribute('aria-describedby', errorId);
    field.setAttribute('aria-invalid', 'true');
  }

  clearFieldError(field, removeClass = true) {
    if (removeClass) {
      field.classList.remove('error');
    }
    
    // Remove error message
    const errorElement = field.parentNode.querySelector('.form-error');
    if (errorElement) {
      errorElement.remove();
    }
    
    // Clear ARIA attributes
    field.removeAttribute('aria-describedby');
    field.removeAttribute('aria-invalid');
  }

  setFormLoading(form, loading) {
    const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
    
    if (loading) {
      form.classList.add('loading');
      if (submitButton) {
        submitButton.classList.add('btn-loading');
        submitButton.disabled = true;
      }
    } else {
      form.classList.remove('loading');
      if (submitButton) {
        submitButton.classList.remove('btn-loading');
        submitButton.disabled = false;
      }
    }
  }

  closeAlert(alert) {
    alert.style.opacity = '0';
    alert.style.transform = 'translateY(-10px)';
    
    setTimeout(() => {
      alert.remove();
    }, 300);
  }

  // Public methods
  addValidationRule(name, validator) {
    this.validators.set(name, validator);
  }

  removeValidationRule(name) {
    this.validators.delete(name);
  }

  validateFormById(formId) {
    const form = document.getElementById(formId);
    return form ? this.validateForm(form) : false;
  }

  resetForm(form) {
    if (typeof form === 'string') {
      form = document.getElementById(form);
    }
    
    if (!form) return;
    
    form.reset();
    
    // Clear all error states
    form.querySelectorAll('.error').forEach(field => {
      this.clearFieldError(field);
    });
    
    // Remove loading state
    this.setFormLoading(form, false);
  }

  showAlert(message, type = 'info', container = document.body) {
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.setAttribute('data-alert', '');
    alert.innerHTML = `
      <i class="fas fa-${this.getAlertIcon(type)}" aria-hidden="true"></i>
      <span>${message}</span>
      <button type="button" data-alert-close aria-label="Close alert">
        <i class="fas fa-times" aria-hidden="true"></i>
      </button>
    `;
    
    container.appendChild(alert);
    
    // Auto-close after 5 seconds
    setTimeout(() => {
      this.closeAlert(alert);
    }, 5000);
    
    return alert;
  }

  getAlertIcon(type) {
    const icons = {
      success: 'check-circle',
      error: 'exclamation-circle',
      warning: 'exclamation-triangle',
      info: 'info-circle'
    };
    return icons[type] || 'info-circle';
  }

  // Reinitialize for new content
  reinitialize() {
    this.findForms();
    this.setupAlertHandlers();
  }
}
