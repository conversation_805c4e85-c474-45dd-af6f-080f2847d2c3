<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Sangguniang Bayan Ordinance System{% endblock %}</title>

    <!-- Meta tags for SEO and PWA -->
    <meta name="description" content="Municipality of Dumingag Sangguniang Bayan Ordinance System - Access municipal ordinances and local legislation">
    <meta name="keywords" content="Dumingag, ordinances, municipal, legislation, Sangguniang Bayan">
    <meta name="author" content="Municipality of Dumingag">
    <meta name="theme-color" content="#123458">

    <!-- Open Graph tags -->
    <meta property="og:title" content="{% block og_title %}Sangguniang Bayan Ordinance System{% endblock %}">
    <meta property="og:description" content="{% block og_description %}Access municipal ordinances and local legislation for the Municipality of Dumingag{% endblock %}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.build_absolute_uri }}">

    <!-- Preload critical resources -->
    {% load static %}
    <link rel="preload" href="{% static 'css/custom.css' %}" as="style">
    <link rel="preload" href="{% static 'js/app.js' %}" as="script">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">

    <!-- External libraries -->
    <script src="https://unpkg.com/htmx.org@1.9.10" defer></script>

    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="navbar-container">
            <div class="navbar-content">
                <!-- Brand -->
                <a href="{% url 'ordinances:home' %}" class="navbar-brand">
                    <div class="navbar-logo">
                        <img src="{% static 'img/dumingag-logo.png' %}"
                             alt="Municipality of Dumingag Logo"
                             loading="lazy">
                    </div>
                    <div class="navbar-title">
                        <h1>Municipality of Dumingag</h1>
                        <p class="navbar-subtitle">Ordinance System</p>
                    </div>
                </a>

                <!-- Desktop Navigation -->
                <div class="navbar-nav desktop">
                    <a href="{% url 'ordinances:home' %}" class="nav-link">
                        <i class="fas fa-home" aria-hidden="true"></i>
                        Home
                    </a>
                    <a href="{% url 'ordinances:ordinance_list' %}" class="nav-link">
                        <i class="fas fa-file-alt" aria-hidden="true"></i>
                        Ordinances
                    </a>
                </div>

                <!-- Auth Links -->
                <div class="navbar-auth">
                    {% if user.is_authenticated %}
                        <a href="{% url 'ordinances:admin_dashboard' %}" class="auth-link">
                            <i class="fas fa-tachometer-alt" aria-hidden="true"></i>
                            Dashboard
                        </a>
                        <a href="{% url 'admin:index' %}" class="auth-link primary">
                            <i class="fas fa-cog" aria-hidden="true"></i>
                            Admin
                        </a>
                        <form method="post" action="{% url 'admin:logout' %}" class="inline">
                            {% csrf_token %}
                            <button type="submit" class="auth-link">
                                <i class="fas fa-sign-out-alt" aria-hidden="true"></i>
                                Logout
                            </button>
                        </form>
                    {% else %}
                        <a href="{% url 'admin:login' %}" class="auth-link primary">
                            <i class="fas fa-sign-in-alt" aria-hidden="true"></i>
                            Admin Login
                        </a>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <button class="mobile-menu-button" aria-expanded="false" aria-label="Toggle navigation menu">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="mobile-nav">
            <div class="mobile-nav-content">
                <div class="mobile-nav-links">
                    <a href="{% url 'ordinances:home' %}" class="mobile-nav-link">
                        <i class="fas fa-home" aria-hidden="true"></i>
                        Home
                    </a>
                    <a href="{% url 'ordinances:ordinance_list' %}" class="mobile-nav-link">
                        <i class="fas fa-file-alt" aria-hidden="true"></i>
                        Ordinances
                    </a>
                </div>

                <div class="mobile-nav-auth">
                    {% if user.is_authenticated %}
                        <a href="{% url 'ordinances:admin_dashboard' %}" class="mobile-nav-link">
                            <i class="fas fa-tachometer-alt" aria-hidden="true"></i>
                            Dashboard
                        </a>
                        <a href="{% url 'admin:index' %}" class="mobile-nav-link">
                            <i class="fas fa-cog" aria-hidden="true"></i>
                            Admin
                        </a>
                        <form method="post" action="{% url 'admin:logout' %}">
                            {% csrf_token %}
                            <button type="submit" class="mobile-nav-link w-full text-left">
                                <i class="fas fa-sign-out-alt" aria-hidden="true"></i>
                                Logout
                            </button>
                        </form>
                    {% else %}
                        <a href="{% url 'admin:login' %}" class="mobile-nav-link">
                            <i class="fas fa-sign-in-alt" aria-hidden="true"></i>
                            Admin Login
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="container" style="margin-top: var(--space-4);">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}" data-alert>
                    <i class="fas fa-{% if message.tags == 'error' %}exclamation-circle{% elif message.tags == 'success' %}check-circle{% elif message.tags == 'warning' %}exclamation-triangle{% else %}info-circle{% endif %}" aria-hidden="true"></i>
                    <span>{{ message }}</span>
                    <button type="button" class="alert-close" data-alert-close aria-label="Close alert">
                        <i class="fas fa-times" aria-hidden="true"></i>
                    </button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main>
        {% block content %}
        {% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-gradient-accent text-light section">
        <div class="container">
            <div class="text-center">
                <div class="flex-center mb-6">
                    <div class="navbar-logo mr-4">
                        <img src="{% static 'img/dumingag-logo.png' %}"
                             alt="Municipality of Dumingag Logo"
                             loading="lazy">
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-light">Municipality of Dumingag</h3>
                        <p class="text-secondary-light">Sangguniang Bayan Ordinance System</p>
                    </div>
                </div>

                <p class="text-secondary-light mb-2">
                    &copy; {% now "Y" %} Municipality of Dumingag. All rights reserved.
                </p>
                <p class="text-secondary-light text-sm">
                    Committed to transparent governance and public service excellence.
                </p>
            </div>
        </div>
    </footer>

    <!-- Loading indicator -->
    <div class="navbar-loading htmx-indicator" id="loading-indicator">
        <div class="spinner"></div>
        <span>Loading...</span>
    </div>

    <!-- Back to top button -->
    <button class="btn-fab back-to-top" aria-label="Back to top" style="display: none;">
        <i class="fas fa-chevron-up" aria-hidden="true"></i>
    </button>

    <!-- JavaScript -->
    <script type="module" src="{% static 'js/app.js' %}"></script>
    <script src="https://kit.fontawesome.com/your-kit-id.js" crossorigin="anonymous"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>