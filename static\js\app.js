/**
 * Main Application JavaScript
 * Sangguniang Bayan Ordinance System
 */

// Import utilities and components
import { ThemeManager } from './utils/theme.js';
import { AnimationManager } from './utils/animations.js';
import { FormHandler } from './utils/forms.js';
import { NavigationComponent } from './components/navigation.js';
import { SearchComponent } from './components/search.js';
import { StatsComponent } from './components/stats.js';
import { LoadingComponent } from './components/loading.js';

class App {
  constructor() {
    this.theme = new ThemeManager();
    this.animations = new AnimationManager();
    this.forms = new FormHandler();
    this.navigation = new NavigationComponent();
    this.search = new SearchComponent();
    this.stats = new StatsComponent();
    this.loading = new LoadingComponent();
    
    this.init();
  }

  init() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.onDOMReady());
    } else {
      this.onDOMReady();
    }
  }

  onDOMReady() {
    console.log('🚀 Sangguniang Bayan Ordinance System initialized');
    
    // Initialize core systems
    this.initializeTheme();
    this.initializeComponents();
    this.initializeEventListeners();
    this.initializeAnimations();
    this.initializeAccessibility();
    
    // Initialize page-specific features
    this.initializePageFeatures();
    
    // Show app is ready
    this.showAppReady();
  }

  initializeTheme() {
    this.theme.init();
    console.log('✅ Theme system initialized');
  }

  initializeComponents() {
    // Initialize all components
    this.navigation.init();
    this.search.init();
    this.stats.init();
    this.loading.init();
    
    console.log('✅ Components initialized');
  }

  initializeEventListeners() {
    // Global event listeners
    this.setupGlobalEvents();
    this.setupHTMXEvents();
    this.setupFormEvents();
    
    console.log('✅ Event listeners initialized');
  }

  initializeAnimations() {
    // Initialize scroll animations and other effects
    this.animations.init();
    this.setupScrollEffects();
    
    console.log('✅ Animations initialized');
  }

  initializeAccessibility() {
    // Setup accessibility features
    this.setupKeyboardNavigation();
    this.setupFocusManagement();
    this.setupScreenReaderSupport();
    
    console.log('✅ Accessibility features initialized');
  }

  initializePageFeatures() {
    // Initialize features based on current page
    const page = this.getCurrentPage();
    
    switch (page) {
      case 'home':
        this.initializeHomePage();
        break;
      case 'ordinances':
        this.initializeOrdinancesPage();
        break;
      case 'admin':
        this.initializeAdminPage();
        break;
      default:
        this.initializeDefaultPage();
    }
    
    console.log(`✅ Page-specific features initialized for: ${page}`);
  }

  getCurrentPage() {
    const path = window.location.pathname;
    if (path === '/' || path.includes('home')) return 'home';
    if (path.includes('ordinances')) return 'ordinances';
    if (path.includes('admin')) return 'admin';
    return 'default';
  }

  initializeHomePage() {
    // Home page specific initialization
    this.initializeHeroSection();
    this.initializeStatsCounters();
    this.initializeOfficialCards();
  }

  initializeOrdinancesPage() {
    // Ordinances page specific initialization
    this.initializeSearchFilters();
    this.initializePagination();
  }

  initializeAdminPage() {
    // Admin page specific initialization
    this.initializeAdminForms();
    this.initializeDataTables();
  }

  initializeDefaultPage() {
    // Default page initialization
    this.initializeBasicFeatures();
  }

  setupGlobalEvents() {
    // Window events
    window.addEventListener('resize', this.debounce(() => {
      this.handleResize();
    }, 250));

    window.addEventListener('scroll', this.throttle(() => {
      this.handleScroll();
    }, 16));

    // Document events
    document.addEventListener('click', (e) => {
      this.handleGlobalClick(e);
    });

    document.addEventListener('keydown', (e) => {
      this.handleGlobalKeydown(e);
    });
  }

  setupHTMXEvents() {
    // HTMX event listeners
    document.addEventListener('htmx:beforeRequest', (e) => {
      this.loading.show();
    });

    document.addEventListener('htmx:afterRequest', (e) => {
      this.loading.hide();
    });

    document.addEventListener('htmx:afterSettle', (e) => {
      // Re-initialize components for new content
      this.animations.reinitialize(e.target);
    });
  }

  setupFormEvents() {
    // Form validation and submission
    this.forms.init();
  }

  setupScrollEffects() {
    // Parallax and scroll-based animations
    this.animations.setupScrollEffects();
  }

  setupKeyboardNavigation() {
    // Keyboard navigation support
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
      }
    });

    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation');
    });
  }

  setupFocusManagement() {
    // Focus management for modals and dynamic content
    this.focusManager = {
      stack: [],
      trap: (element) => {
        this.focusManager.stack.push(document.activeElement);
        element.focus();
      },
      release: () => {
        const previous = this.focusManager.stack.pop();
        if (previous) previous.focus();
      }
    };
  }

  setupScreenReaderSupport() {
    // Screen reader announcements
    this.announcer = document.createElement('div');
    this.announcer.setAttribute('aria-live', 'polite');
    this.announcer.setAttribute('aria-atomic', 'true');
    this.announcer.className = 'sr-only';
    document.body.appendChild(this.announcer);
  }

  announce(message) {
    this.announcer.textContent = message;
    setTimeout(() => {
      this.announcer.textContent = '';
    }, 1000);
  }

  handleResize() {
    // Handle window resize
    this.navigation.handleResize();
    this.animations.handleResize();
  }

  handleScroll() {
    // Handle window scroll
    this.navigation.handleScroll();
    this.animations.handleScroll();
  }

  handleGlobalClick(e) {
    // Handle global clicks for dropdowns, modals, etc.
    if (!e.target.closest('.dropdown')) {
      this.closeAllDropdowns();
    }
  }

  handleGlobalKeydown(e) {
    // Handle global keyboard shortcuts
    if (e.key === 'Escape') {
      this.closeAllModals();
      this.closeAllDropdowns();
    }
  }

  closeAllDropdowns() {
    document.querySelectorAll('.dropdown.open').forEach(dropdown => {
      dropdown.classList.remove('open');
    });
  }

  closeAllModals() {
    document.querySelectorAll('.modal.open').forEach(modal => {
      modal.classList.remove('open');
    });
  }

  showAppReady() {
    // Remove loading states and show app is ready
    document.body.classList.add('app-ready');
    
    // Trigger initial animations
    this.animations.triggerInitialAnimations();
  }

  // Utility functions
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
}

// Initialize app when script loads
const app = new App();

// Export for global access
window.SBOApp = app;
