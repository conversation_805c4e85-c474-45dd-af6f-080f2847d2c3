/* Card Component Styles */

.card {
  background: var(--bg-white);
  border-radius: var(--radius-2xl);
  box-shadow: 0 4px 20px var(--shadow-light);
  overflow: hidden;
  transition: all var(--transition-normal);
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-accent), var(--color-secondary));
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px var(--shadow-medium);
}

.card:hover::before {
  opacity: 1;
}

/* Card variants */
.card.elevated {
  box-shadow: 0 8px 32px var(--shadow-medium);
}

.card.flat {
  box-shadow: none;
  border: 1px solid var(--border-light);
}

.card.glass {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Card header */
.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-light);
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.card-subtitle {
  font-size: var(--text-sm);
  color: var(--text-muted);
  margin: var(--space-1) 0 0 0;
}

/* Card body */
.card-body {
  padding: var(--space-6);
}

.card-body p:last-child {
  margin-bottom: 0;
}

/* Card footer */
.card-footer {
  padding: var(--space-4) var(--space-6);
  border-top: 1px solid var(--border-light);
  background: var(--bg-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Official cards (for officials section) */
.official-card {
  background: var(--bg-white);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: 0 8px 32px var(--shadow-light);
  transition: all var(--transition-normal);
  transform-style: preserve-3d;
  position: relative;
}

.official-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(18, 52, 88, 0.05) 100%);
  opacity: 0;
  transition: opacity var(--transition-normal);
  pointer-events: none;
  z-index: 1;
}

.official-card:hover {
  transform: translateY(-8px) rotateY(2deg);
  box-shadow: 0 20px 60px var(--shadow-dark);
}

.official-card:hover::before {
  opacity: 1;
}

.official-card-header {
  height: 16rem;
  background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-accent-dark) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.official-card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.official-card-logo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12rem;
  height: 12rem;
  opacity: 0.1;
  z-index: 0;
}

.official-card-avatar {
  width: 5rem;
  height: 5rem;
  background: var(--bg-white);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
  overflow: hidden;
  box-shadow: 0 4px 16px var(--shadow-medium);
}

.official-card-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.official-card-badge {
  position: absolute;
  top: var(--space-4);
  right: var(--space-4);
  background: var(--color-secondary);
  color: var(--color-accent);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
  z-index: 3;
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.official-card-body {
  padding: var(--space-6);
  position: relative;
  z-index: 2;
}

.official-card-name {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.official-card-position {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--color-accent);
  margin: 0 0 var(--space-4) 0;
}

.official-card-bio {
  font-size: var(--text-sm);
  color: var(--text-muted);
  line-height: var(--leading-relaxed);
  margin: 0 0 var(--space-4) 0;
}

.official-card-achievements {
  margin-top: var(--space-4);
}

.official-card-achievements h4 {
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-2) 0;
}

.achievement-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
  margin-bottom: var(--space-2);
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.achievement-item:last-child {
  margin-bottom: 0;
}

.achievement-icon {
  color: var(--color-success);
  margin-top: 2px;
  flex-shrink: 0;
}

/* Stats cards */
.stats-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-2xl);
  padding: var(--space-6);
  text-align: center;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px var(--shadow-medium);
}

.stats-card:hover::before {
  opacity: 1;
}

.stats-icon {
  font-size: var(--text-3xl);
  margin-bottom: var(--space-2);
}

.stats-number {
  font-size: var(--text-4xl);
  font-weight: 700;
  color: var(--color-accent);
  margin: 0;
  line-height: 1;
}

.stats-label {
  font-size: var(--text-sm);
  color: var(--text-muted);
  margin: var(--space-2) 0 0 0;
}

/* Ordinance cards */
.ordinance-card {
  background: var(--bg-white);
  border-radius: var(--radius-xl);
  box-shadow: 0 4px 20px var(--shadow-light);
  overflow: hidden;
  transition: all var(--transition-normal);
  border: 1px solid var(--border-light);
}

.ordinance-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px var(--shadow-medium);
  border-color: var(--color-accent-light);
}

.ordinance-card-body {
  padding: var(--space-6);
}

.ordinance-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-4);
}

.ordinance-number {
  background: var(--color-accent);
  color: var(--text-light);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: var(--text-xs);
  font-weight: 600;
}

.ordinance-year {
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.ordinance-title {
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--space-3) 0;
  line-height: var(--leading-tight);
}

.ordinance-excerpt {
  font-size: var(--text-sm);
  color: var(--text-muted);
  line-height: var(--leading-relaxed);
  margin: 0 0 var(--space-4) 0;
}

.ordinance-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ordinance-category {
  font-size: var(--text-xs);
  color: var(--text-muted);
}

.ordinance-link {
  color: var(--color-accent);
  font-size: var(--text-sm);
  font-weight: 500;
  transition: color var(--transition-fast);
}

.ordinance-link:hover {
  color: var(--color-accent-dark);
}

/* Responsive design */
@media (max-width: 768px) {
  .card-header,
  .card-body,
  .card-footer {
    padding: var(--space-4);
  }
  
  .official-card-header {
    height: 12rem;
  }
  
  .official-card-avatar {
    width: 4rem;
    height: 4rem;
  }
  
  .official-card-body {
    padding: var(--space-4);
  }
  
  .stats-card {
    padding: var(--space-4);
  }
  
  .stats-number {
    font-size: var(--text-3xl);
  }
}
