/* Elegant Theme Configuration */
/* Color Palette: #F1EFEC, #D4C9BE, #123458, #030303 */

:root {
  /* Primary Colors */
  --color-primary: #F1EFEC;        /* Light cream - main background */
  --color-secondary: #D4C9BE;      /* Warm beige - secondary elements */
  --color-accent: #123458;         /* Deep navy - accent and text */
  --color-dark: #030303;           /* Near black - primary text */
  
  /* Color Variations */
  --color-primary-light: #F8F7F5;
  --color-primary-dark: #E8E5E0;
  --color-secondary-light: #E2D9D0;
  --color-secondary-dark: #C4B7AA;
  --color-accent-light: #1E4A73;
  --color-accent-dark: #0D2340;
  
  /* Semantic Colors */
  --color-success: #10B981;
  --color-warning: #F59E0B;
  --color-error: #EF4444;
  --color-info: var(--color-accent);
  
  /* Text Colors */
  --text-primary: var(--color-dark);
  --text-secondary: var(--color-accent);
  --text-muted: #6B7280;
  --text-light: var(--color-primary);
  
  /* Background Colors */
  --bg-primary: var(--color-primary);
  --bg-secondary: var(--color-secondary);
  --bg-accent: var(--color-accent);
  --bg-dark: var(--color-dark);
  --bg-white: #FFFFFF;
  --bg-overlay: rgba(3, 3, 3, 0.5);
  
  /* Border Colors */
  --border-light: var(--color-secondary);
  --border-medium: var(--color-accent-light);
  --border-dark: var(--color-accent);
  
  /* Shadow Colors */
  --shadow-light: rgba(18, 52, 88, 0.1);
  --shadow-medium: rgba(18, 52, 88, 0.15);
  --shadow-dark: rgba(18, 52, 88, 0.25);
  
  /* Typography */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;
  
  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;
  
  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;
  --space-32: 8rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* Breakpoints (for reference in JS) */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Dark mode support (optional) */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: var(--color-dark);
    --bg-secondary: #1A1A1A;
    --text-primary: var(--color-primary);
    --text-secondary: var(--color-secondary);
    --border-light: #2A2A2A;
    --border-medium: #3A3A3A;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --color-accent: #000000;
    --text-primary: #000000;
    --border-dark: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0ms;
    --transition-normal: 0ms;
    --transition-slow: 0ms;
  }
}
