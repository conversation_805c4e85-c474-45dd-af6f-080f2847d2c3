/* Form Component Styles */

.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.form-label.required::after {
  content: ' *';
  color: var(--color-error);
}

/* Input styles */
.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background-color: var(--bg-white);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  transition: all var(--transition-fast);
  font-family: inherit;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--color-accent);
  box-shadow: 0 0 0 4px rgba(18, 52, 88, 0.1);
  background-color: var(--bg-white);
}

.form-input:hover,
.form-select:hover,
.form-textarea:hover {
  border-color: var(--color-accent-light);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--text-muted);
}

/* Input variants */
.form-input.large {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-lg);
}

.form-input.small {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-sm);
}

/* Textarea */
.form-textarea {
  resize: vertical;
  min-height: 6rem;
}

/* Select */
.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--space-3) center;
  background-repeat: no-repeat;
  background-size: 1rem;
  padding-right: var(--space-10);
  appearance: none;
}

.form-select:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23123458' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
}

/* Input with icon */
.form-input-group {
  position: relative;
}

.form-input-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: var(--text-base);
  pointer-events: none;
  z-index: 1;
}

.form-input-group .form-input {
  padding-left: var(--space-10);
}

.form-input-group .form-input:focus + .form-input-icon {
  color: var(--color-accent);
}

/* Search form */
.search-form {
  position: relative;
  max-width: 64rem;
  margin: 0 auto;
}

.search-input-group {
  position: relative;
  margin-bottom: var(--space-6);
}

.search-input {
  width: 100%;
  padding: var(--space-4) var(--space-6);
  padding-right: 8rem;
  font-size: var(--text-lg);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-2xl);
  background: var(--bg-white);
  transition: all var(--transition-fast);
  box-shadow: 0 4px 20px var(--shadow-light);
}

.search-input:focus {
  border-color: var(--color-accent);
  box-shadow: 0 4px 20px var(--shadow-medium), 0 0 0 4px rgba(18, 52, 88, 0.1);
}

.search-button {
  position: absolute;
  right: var(--space-2);
  top: 50%;
  transform: translateY(-50%);
  background: var(--color-accent);
  color: var(--text-light);
  border: none;
  padding: var(--space-2) var(--space-6);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.search-button:hover {
  background: var(--color-accent-dark);
  transform: translateY(-50%) translateY(-1px);
  box-shadow: 0 4px 12px var(--shadow-medium);
}

.search-button:active {
  transform: translateY(-50%) translateY(0);
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: translateY(-50%);
}

/* Filter grid */
.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

.filter-group {
  position: relative;
}

.filter-select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  padding-left: var(--space-10);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  background: var(--bg-white);
  font-size: var(--text-base);
  transition: all var(--transition-fast);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--space-3) center;
  background-repeat: no-repeat;
  background-size: 1rem;
  padding-right: var(--space-10);
}

.filter-select:focus {
  border-color: var(--color-accent);
  box-shadow: 0 0 0 4px rgba(18, 52, 88, 0.1);
}

.filter-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: var(--text-base);
  pointer-events: none;
}

/* Form validation states */
.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: var(--color-error);
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

.form-input.success,
.form-select.success,
.form-textarea.success {
  border-color: var(--color-success);
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}

.form-error {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-2);
  font-size: var(--text-sm);
  color: var(--color-error);
}

.form-success {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-2);
  font-size: var(--text-sm);
  color: var(--color-success);
}

.form-help {
  margin-top: var(--space-2);
  font-size: var(--text-sm);
  color: var(--text-muted);
}

/* Checkbox and radio */
.form-checkbox,
.form-radio {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  margin-bottom: var(--space-3);
}

.form-checkbox input,
.form-radio input {
  width: 1.25rem;
  height: 1.25rem;
  margin: 0;
  accent-color: var(--color-accent);
}

.form-checkbox label,
.form-radio label {
  font-size: var(--text-sm);
  color: var(--text-primary);
  cursor: pointer;
  margin: 0;
}

/* File input */
.form-file {
  position: relative;
  display: inline-block;
  cursor: pointer;
}

.form-file input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.form-file-label {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-4);
  background: var(--bg-secondary);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--text-primary);
  transition: all var(--transition-fast);
  cursor: pointer;
}

.form-file:hover .form-file-label {
  background: var(--color-accent-light);
  color: var(--text-light);
  border-color: var(--color-accent);
}

/* Responsive design */
@media (max-width: 768px) {
  .search-input {
    padding: var(--space-3) var(--space-4);
    padding-right: 7rem;
    font-size: var(--text-base);
  }
  
  .search-button {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
  }
  
  .filter-grid {
    grid-template-columns: 1fr;
  }
  
  .form-input.large {
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-base);
  }
}
