/**
 * Animation Management Utility
 * Handles scroll animations, transitions, and effects
 */

export class AnimationManager {
  constructor() {
    this.observers = new Map();
    this.animatedElements = new Set();
    this.reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    this.scrollElements = [];
    this.parallaxElements = [];
  }

  init() {
    this.setupIntersectionObserver();
    this.setupScrollAnimations();
    this.setupParallaxEffects();
    this.setupReducedMotionListener();
    this.initializeExistingElements();
  }

  setupIntersectionObserver() {
    // Create intersection observer for scroll animations
    this.scrollObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          this.animateElement(entry.target);
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    this.observers.set('scroll', this.scrollObserver);
  }

  setupScrollAnimations() {
    // Find and setup elements with scroll animations
    const animatedElements = document.querySelectorAll('[data-animate]');
    animatedElements.forEach(element => {
      this.addScrollAnimation(element);
    });
  }

  setupParallaxEffects() {
    // Setup parallax elements
    const parallaxElements = document.querySelectorAll('[data-parallax]');
    parallaxElements.forEach(element => {
      this.addParallaxEffect(element);
    });
  }

  setupReducedMotionListener() {
    // Listen for reduced motion preference changes
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    mediaQuery.addEventListener('change', (e) => {
      this.reducedMotion = e.matches;
      this.updateAnimationsForMotionPreference();
    });
  }

  initializeExistingElements() {
    // Initialize animations for elements already in viewport
    const elementsInView = document.querySelectorAll('[data-animate]');
    elementsInView.forEach(element => {
      const rect = element.getBoundingClientRect();
      if (rect.top < window.innerHeight && rect.bottom > 0) {
        this.animateElement(element);
      }
    });
  }

  addScrollAnimation(element) {
    if (this.reducedMotion) {
      element.classList.add('animate-fade-in');
      return;
    }

    const animationType = element.dataset.animate || 'fade-in-up';
    const delay = element.dataset.animateDelay || '0';
    const duration = element.dataset.animateDuration || '600';

    // Set initial state
    element.style.opacity = '0';
    element.style.transform = this.getInitialTransform(animationType);
    element.style.transition = `all ${duration}ms ease-out ${delay}ms`;

    // Add to observer
    this.scrollObserver.observe(element);
    this.scrollElements.push(element);
  }

  addParallaxEffect(element) {
    if (this.reducedMotion) return;

    const speed = parseFloat(element.dataset.parallax) || 0.5;
    const direction = element.dataset.parallaxDirection || 'vertical';
    
    this.parallaxElements.push({
      element,
      speed,
      direction,
      initialOffset: this.getElementOffset(element)
    });
  }

  animateElement(element) {
    if (this.animatedElements.has(element)) return;

    const animationType = element.dataset.animate || 'fade-in-up';
    
    if (this.reducedMotion) {
      element.style.opacity = '1';
      element.style.transform = 'none';
      this.animatedElements.add(element);
      return;
    }

    // Apply animation
    element.style.opacity = '1';
    element.style.transform = 'translateY(0) translateX(0) scale(1) rotate(0)';
    
    // Add animation class for additional effects
    element.classList.add(`animate-${animationType}`);
    
    this.animatedElements.add(element);

    // Trigger custom event
    element.dispatchEvent(new CustomEvent('elementAnimated', {
      detail: { animationType }
    }));
  }

  getInitialTransform(animationType) {
    const transforms = {
      'fade-in': 'none',
      'fade-in-up': 'translateY(30px)',
      'fade-in-down': 'translateY(-30px)',
      'fade-in-left': 'translateX(-30px)',
      'fade-in-right': 'translateX(30px)',
      'scale-in': 'scale(0.8)',
      'rotate-in': 'rotate(-10deg)',
      'slide-in-left': 'translateX(-100%)',
      'slide-in-right': 'translateX(100%)'
    };

    return transforms[animationType] || 'translateY(30px)';
  }

  getElementOffset(element) {
    const rect = element.getBoundingClientRect();
    return {
      top: rect.top + window.pageYOffset,
      left: rect.left + window.pageXOffset
    };
  }

  handleScroll() {
    if (this.reducedMotion) return;

    const scrollY = window.pageYOffset;
    
    // Handle parallax effects
    this.parallaxElements.forEach(({ element, speed, direction, initialOffset }) => {
      const elementTop = initialOffset.top;
      const elementHeight = element.offsetHeight;
      const windowHeight = window.innerHeight;
      
      // Check if element is in viewport
      if (scrollY + windowHeight > elementTop && scrollY < elementTop + elementHeight) {
        const yPos = -(scrollY - elementTop) * speed;
        
        if (direction === 'vertical') {
          element.style.transform = `translateY(${yPos}px)`;
        } else if (direction === 'horizontal') {
          element.style.transform = `translateX(${yPos}px)`;
        }
      }
    });

    // Handle scroll-based animations
    this.handleScrollBasedAnimations(scrollY);
  }

  handleScrollBasedAnimations(scrollY) {
    // Navbar background opacity
    const navbar = document.querySelector('.navbar');
    if (navbar) {
      const opacity = Math.min(scrollY / 100, 1);
      navbar.style.setProperty('--scroll-opacity', opacity);
    }

    // Scroll indicator
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
      const opacity = Math.max(1 - scrollY / 300, 0);
      scrollIndicator.style.opacity = opacity;
    }

    // Back to top button
    const backToTop = document.querySelector('.back-to-top');
    if (backToTop) {
      if (scrollY > 500) {
        backToTop.classList.add('visible');
      } else {
        backToTop.classList.remove('visible');
      }
    }
  }

  handleResize() {
    // Recalculate parallax offsets
    this.parallaxElements.forEach(item => {
      item.initialOffset = this.getElementOffset(item.element);
    });
  }

  updateAnimationsForMotionPreference() {
    if (this.reducedMotion) {
      // Disable all animations
      document.body.classList.add('reduce-motion');
      
      // Make all animated elements visible immediately
      this.scrollElements.forEach(element => {
        element.style.opacity = '1';
        element.style.transform = 'none';
        element.style.transition = 'none';
      });
    } else {
      document.body.classList.remove('reduce-motion');
    }
  }

  reinitialize(container = document) {
    // Reinitialize animations for new content (e.g., HTMX updates)
    const newElements = container.querySelectorAll('[data-animate]:not(.animated)');
    newElements.forEach(element => {
      this.addScrollAnimation(element);
    });

    const newParallaxElements = container.querySelectorAll('[data-parallax]:not(.parallax-initialized)');
    newParallaxElements.forEach(element => {
      this.addParallaxEffect(element);
      element.classList.add('parallax-initialized');
    });
  }

  triggerInitialAnimations() {
    // Trigger hero animations
    const heroElements = document.querySelectorAll('.hero [data-animate]');
    heroElements.forEach((element, index) => {
      setTimeout(() => {
        this.animateElement(element);
      }, index * 100);
    });
  }

  // Utility methods for manual animations
  fadeIn(element, duration = 300) {
    if (this.reducedMotion) {
      element.style.opacity = '1';
      return Promise.resolve();
    }

    return new Promise(resolve => {
      element.style.opacity = '0';
      element.style.transition = `opacity ${duration}ms ease-out`;
      
      requestAnimationFrame(() => {
        element.style.opacity = '1';
        setTimeout(resolve, duration);
      });
    });
  }

  fadeOut(element, duration = 300) {
    if (this.reducedMotion) {
      element.style.opacity = '0';
      return Promise.resolve();
    }

    return new Promise(resolve => {
      element.style.transition = `opacity ${duration}ms ease-out`;
      element.style.opacity = '0';
      setTimeout(resolve, duration);
    });
  }

  slideDown(element, duration = 300) {
    if (this.reducedMotion) {
      element.style.display = 'block';
      return Promise.resolve();
    }

    return new Promise(resolve => {
      element.style.height = '0';
      element.style.overflow = 'hidden';
      element.style.transition = `height ${duration}ms ease-out`;
      element.style.display = 'block';
      
      const targetHeight = element.scrollHeight + 'px';
      
      requestAnimationFrame(() => {
        element.style.height = targetHeight;
        setTimeout(() => {
          element.style.height = '';
          element.style.overflow = '';
          element.style.transition = '';
          resolve();
        }, duration);
      });
    });
  }

  slideUp(element, duration = 300) {
    if (this.reducedMotion) {
      element.style.display = 'none';
      return Promise.resolve();
    }

    return new Promise(resolve => {
      element.style.height = element.offsetHeight + 'px';
      element.style.overflow = 'hidden';
      element.style.transition = `height ${duration}ms ease-out`;
      
      requestAnimationFrame(() => {
        element.style.height = '0';
        setTimeout(() => {
          element.style.display = 'none';
          element.style.height = '';
          element.style.overflow = '';
          element.style.transition = '';
          resolve();
        }, duration);
      });
    });
  }

  // Cleanup method
  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
    this.animatedElements.clear();
    this.scrollElements = [];
    this.parallaxElements = [];
  }
}
